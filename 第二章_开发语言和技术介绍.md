# 第二章 开发语言和技术介绍

## 2.1 后端技术栈

### 2.1.1 Java语言
Java是一种面向对象的编程语言，具有"一次编写，到处运行"的特点。本项目选择Java作为后端开发语言的主要原因包括：

1. **平台无关性**：Java程序可以在任何支持Java虚拟机的平台上运行，具有良好的跨平台特性
2. **面向对象**：Java完全支持面向对象编程，有利于代码的模块化和重用
3. **安全性**：Java提供了完善的安全机制，包括字节码验证、安全管理器等
4. **丰富的API**：Java提供了大量的标准API和第三方库，简化了开发工作
5. **企业级应用支持**：Java在企业级应用开发方面具有成熟的解决方案

本项目使用Java 8版本，充分利用了Lambda表达式、Stream API等新特性，提高了代码的简洁性和可读性。

### 2.1.2 Spring框架
Spring是一个开源的Java应用程序框架，为企业级应用开发提供了全面的编程和配置模型。本项目中Spring的主要作用包括：

1. **依赖注入（DI）**：通过IoC容器管理对象的创建和依赖关系，降低了组件间的耦合度
2. **面向切面编程（AOP）**：提供了声明式事务管理、日志记录等横切关注点的解决方案
3. **集成支持**：为各种技术的集成提供了统一的编程模型

**核心配置示例：**
```xml
<bean id="dataSource" class="org.apache.commons.dbcp.BasicDataSource">
    <property name="driverClassName" value="com.mysql.cj.jdbc.Driver"/>
    <property name="url" value="**************************************"/>
    <property name="username" value="root"/>
    <property name="password" value="123456"/>
</bean>
```

### 2.1.3 SpringMVC框架
SpringMVC是Spring框架的一个模块，实现了MVC设计模式，用于构建Web应用程序。其主要特点包括：

1. **清晰的角色分工**：Controller负责处理请求，Model负责数据处理，View负责展示
2. **灵活的配置**：支持注解配置和XML配置两种方式
3. **强大的数据绑定**：自动将请求参数绑定到Java对象
4. **国际化支持**：提供了完善的国际化解决方案

**Controller示例：**
```java
@Controller
@RequestMapping(value = "admin")
public class AdminController {
    @RequestMapping(value = "/adminLogin", method = RequestMethod.POST)
    public String postAdminLogin(ModelMap model,
                                @RequestParam(value = "email") String email,
                                @RequestParam(value = "password") String password,
                                HttpSession session) {
        // 处理管理员登录逻辑
        return "redirect:/admin/adminPage";
    }
}
```

### 2.1.4 MyBatis框架
MyBatis是一个优秀的持久层框架，它支持定制化SQL、存储过程以及高级映射。选择MyBatis的原因：

1. **SQL控制**：开发者可以完全控制SQL语句，适合复杂查询
2. **简单易学**：相比Hibernate等ORM框架，MyBatis学习曲线较平缓
3. **性能优秀**：直接使用SQL，性能接近原生JDBC
4. **灵活映射**：支持复杂的结果集映射

**Mapper接口示例：**
```java
public interface UserMapper {
    @Select("select * from user_table where email = #{email}")
    User getUserByEmail(String email);
    
    @Insert("insert into user_table (name, mobile, email, password) " +
            "values (#{name}, #{mobile}, #{email}, #{password})")
    int insertUser(User user);
}
```

## 2.2 前端技术栈

### 2.2.1 JSP技术
JavaServer Pages（JSP）是一种动态网页技术标准，本项目选择JSP作为视图层技术的原因：

1. **Java集成**：JSP与Java后端无缝集成，可以直接使用Java代码
2. **标签库支持**：支持JSTL等标签库，简化页面开发
3. **组件化**：支持页面包含和组件复用
4. **表达式语言**：提供EL表达式简化数据访问

**JSP页面示例：**
```jsp
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<html>
<head>
    <title>校园二手交易平台</title>
    <link rel="stylesheet" href="<c:url value="/statics/bootstrap-3.3.0/css/bootstrap.css"/>">
</head>
<body>
    <c:forEach var="good" items="${goods}">
        <div class="good-item">
            <h4>${good.name}</h4>
            <p>价格：${good.price}元</p>
        </div>
    </c:forEach>
</body>
</html>
```

### 2.2.2 Bootstrap框架
Bootstrap是一个用于快速开发Web应用程序的前端框架，本项目使用Bootstrap 3.3.0版本：

1. **响应式设计**：自动适配不同屏幕尺寸的设备
2. **丰富的组件**：提供了按钮、表单、导航等常用组件
3. **栅格系统**：灵活的12列栅格系统，便于页面布局
4. **浏览器兼容**：良好的跨浏览器兼容性

### 2.2.3 jQuery库
jQuery是一个快速、简洁的JavaScript库，简化了HTML文档操作、事件处理、动画效果等：

1. **DOM操作**：简化了DOM元素的选择和操作
2. **事件处理**：提供了统一的事件处理机制
3. **AJAX支持**：简化了异步请求的处理
4. **动画效果**：内置了丰富的动画效果

**jQuery使用示例：**
```javascript
$(function() {
    var width = $("#type-bar").width();
    $(window).scroll(function() {
        if ($(document).scrollTop() >= $("#search-bar").height()) {
            $("#type-bar").css({
                "position": "fixed",
                "top": 150 - $("#search-bar").height() + "px",
                "width": width
            });
        }
    });
});
```

## 2.3 数据库技术

### 2.3.1 MySQL数据库
MySQL是一个开源的关系型数据库管理系统，本项目选择MySQL 8.0版本：

1. **开源免费**：降低了项目的开发成本
2. **性能优秀**：在中小型应用中表现出色
3. **易于使用**：提供了友好的管理工具和丰富的文档
4. **广泛支持**：得到了各种编程语言和框架的良好支持

**数据库连接配置：**
```xml
<property name="url" value="********************************************************************************************************************************************************************"/>
```

### 2.3.2 数据库连接池
项目使用Apache Commons DBCP作为数据库连接池：

1. **连接复用**：避免频繁创建和销毁数据库连接
2. **性能提升**：减少了数据库连接的开销
3. **资源管理**：自动管理连接的生命周期
4. **配置灵活**：支持多种配置参数

## 2.4 开发工具和环境

### 2.4.1 集成开发环境（IDE）
本项目推荐使用IntelliJ IDEA或Eclipse作为开发IDE：

1. **代码智能提示**：提供了强大的代码自动完成功能
2. **调试支持**：内置了完善的调试工具
3. **版本控制集成**：支持Git等版本控制系统
4. **插件生态**：丰富的插件支持各种开发需求

### 2.4.2 项目构建工具
使用Apache Maven作为项目构建和依赖管理工具：

1. **依赖管理**：自动下载和管理项目依赖
2. **标准化构建**：提供了标准的项目结构和构建流程
3. **插件支持**：丰富的插件支持各种构建任务
4. **仓库机制**：中央仓库提供了大量的开源库

**Maven配置示例：**
```xml
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-webmvc</artifactId>
    <version>4.3.3.RELEASE</version>
</dependency>
```

## 2.5 服务器和部署技术

### 2.5.1 Web服务器
项目支持多种Web服务器部署：

1. **Apache Tomcat**：轻量级的Java Web服务器，适合开发和测试
2. **Jetty**：高性能的Java Web服务器，支持嵌入式部署

**Tomcat配置：**
```xml
<plugin>
    <groupId>org.apache.tomcat.maven</groupId>
    <artifactId>tomcat7-maven-plugin</artifactId>
    <version>2.2</version>
    <configuration>
        <port>8080</port>
        <path>/</path>
        <uriEncoding>UTF-8</uriEncoding>
    </configuration>
</plugin>
```

### 2.5.2 硬件环境要求
**最低配置要求：**
- CPU：双核2.0GHz以上
- 内存：4GB以上
- 硬盘：50GB可用空间
- 网络：100Mbps带宽

**推荐配置：**
- CPU：四核3.0GHz以上
- 内存：8GB以上
- 硬盘：SSD 100GB以上
- 网络：1Gbps带宽

通过以上技术栈的合理选择和配置，本项目构建了一个稳定、高效、易维护的校园二手交易平台，为用户提供了良好的使用体验。
