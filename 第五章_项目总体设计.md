# 第五章 项目总体设计

## 5.1 项目设计原则

### 5.1.1 系统架构设计原则

1. **分层架构原则**：采用经典的三层架构模式，将表示层、业务逻辑层和数据访问层分离，提高系统的可维护性和可扩展性。

2. **松耦合原则**：各模块之间保持松耦合关系，通过接口进行交互，降低模块间的依赖性。

3. **高内聚原则**：每个模块内部功能高度相关，职责单一明确。

4. **可扩展性原则**：系统设计考虑未来功能扩展的需要，预留扩展接口。

5. **安全性原则**：在系统各层都考虑安全性设计，包括数据验证、权限控制、数据加密等。

### 5.1.2 数据库设计原则

1. **规范化原则**：遵循数据库设计的三范式，减少数据冗余。

2. **完整性原则**：保证数据的实体完整性、参照完整性和用户定义完整性。

3. **一致性原则**：确保数据在任何时候都保持一致状态。

4. **性能优化原则**：合理设计索引，优化查询性能。

### 5.1.3 用户界面设计原则

1. **用户友好原则**：界面简洁直观，操作流程清晰。

2. **一致性原则**：保持界面风格和交互方式的一致性。

3. **响应式原则**：支持多种设备和屏幕尺寸。

4. **可访问性原则**：考虑不同用户群体的使用需求。

## 5.2 项目总体功能结构图

### 5.2.1 系统总体架构图

```mermaid
graph TB
    subgraph "表示层 (Presentation Layer)"
        A1[用户界面]
        A2[管理员界面]
        A3[移动端界面]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        B1[用户管理服务]
        B2[商品管理服务]
        B3[交易管理服务]
        B4[系统管理服务]
    end
    
    subgraph "数据访问层 (Data Access Layer)"
        C1[用户数据访问]
        C2[商品数据访问]
        C3[交易数据访问]
        C4[系统数据访问]
    end
    
    subgraph "数据层 (Data Layer)"
        D1[(MySQL数据库)]
        D2[文件存储系统]
    end
    
    A1 --> B1
    A1 --> B2
    A1 --> B3
    A2 --> B4
    A3 --> B1
    A3 --> B2
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C2 --> D2
```

### 5.2.2 功能模块结构图

```mermaid
graph TD
    A[校园二手交易平台] --> B[前台用户系统]
    A --> C[后台管理系统]
    
    B --> D[用户管理模块]
    B --> E[商品管理模块]
    B --> F[交易管理模块]
    B --> G[个人中心模块]
    
    C --> H[用户管理模块]
    C --> I[商品审核模块]
    C --> J[订单管理模块]
    C --> K[系统设置模块]
    C --> L[数据统计模块]
    
    D --> D1[用户注册]
    D --> D2[用户登录]
    D --> D3[密码管理]
    
    E --> E1[商品浏览]
    E --> E2[商品搜索]
    E --> E3[商品发布]
    E --> E4[商品分类]
    
    F --> F1[订单创建]
    F --> F2[订单跟踪]
    F --> F3[交易评价]
    F --> F4[纠纷处理]
    
    G --> G1[个人信息]
    G --> G2[我的商品]
    G --> G3[我的订单]
    G --> G4[收藏夹]
    
    H --> H1[用户查询]
    H --> H2[用户编辑]
    H --> H3[用户禁用]
    
    I --> I1[商品审核]
    I --> I2[商品上架]
    I --> I3[商品下架]
    
    J --> J1[订单查询]
    J --> J2[订单处理]
    J --> J3[纠纷仲裁]
    
    K --> K1[分类管理]
    K --> K2[系统参数]
    K --> K3[权限管理]
    
    L --> L1[用户统计]
    L --> L2[商品统计]
    L --> L3[交易统计]
```

## 5.3 数据库设计

### 5.3.1 概念设计（E-R图）

#### 5.3.1.1 总体E-R图

```mermaid
erDiagram
    USER ||--o{ GOOD : publishes
    USER ||--o{ ORDER : creates
    USER ||--o{ COLLECT : collects
    USER ||--o{ REVIEW : writes
    GOOD ||--o{ ORDER : involves
    GOOD ||--o{ COLLECT : collected
    GOOD ||--o{ REVIEW : receives
    GOOD ||--o{ IMAGE : has
    GOOD }o--|| FIRST_TYPE : belongs_to
    GOOD }o--|| SECOND_TYPE : belongs_to
    FIRST_TYPE ||--o{ SECOND_TYPE : contains
    ORDER ||--o{ REVIEW : generates
    REVIEW ||--o{ REPLY : has
    USER ||--|| ROLE : has
    USER ||--|| STATUS : has
    
    USER {
        int id PK
        string name
        string mobile
        string email
        string password
        string code
        string photo_url
        int role_id FK
        string gender
        datetime register_date
        int status_id FK
        int creditgrade
        int count
        int grade
    }
    
    GOOD {
        int id PK
        string name
        string photo_url
        int first_type_id FK
        int second_type_id FK
        string describe
        datetime upload_date
        float price
        int status_id FK
        int user_id FK
        datetime update
    }
    
    ORDER {
        int id PK
        string good_name
        string seller
        int seller_id FK
        string customer
        int customer_id FK
        int good_id FK
        int money
        datetime submit_date
        datetime end_date
        int status_id FK
        int order_type
        datetime creditend_date
        int score
    }
```

#### 5.3.1.2 重要实体属性图

**用户实体（USER）**：
- 用户ID（主键）
- 用户姓名
- 手机号码
- 电子邮箱
- 登录密码
- 验证码
- 头像URL
- 角色ID（外键）
- 性别
- 注册日期
- 状态ID（外键）
- 信用等级
- 交易次数
- 积分

**商品实体（GOOD）**：
- 商品ID（主键）
- 商品名称
- 商品图片URL
- 一级分类ID（外键）
- 二级分类ID（外键）
- 商品描述
- 上传日期
- 商品价格
- 状态ID（外键）
- 发布用户ID（外键）
- 更新时间

**订单实体（ORDER）**：
- 订单ID（主键）
- 商品名称
- 卖家姓名
- 卖家ID（外键）
- 买家姓名
- 买家ID（外键）
- 商品ID（外键）
- 交易金额
- 提交日期
- 完成日期
- 状态ID（外键）
- 订单类型
- 信用截止日期
- 评分

### 5.3.2 逻辑设计（数据表设计）

#### 5.3.2.1 用户表（user_table）

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | 11 | NOT NULL | AUTO_INCREMENT | 用户ID，主键 |
| name | VARCHAR | 50 | NULL | NULL | 用户姓名 |
| mobile | VARCHAR | 20 | NULL | NULL | 手机号码 |
| email | VARCHAR | 255 | NULL | NULL | 电子邮箱 |
| password | VARCHAR | 255 | NULL | NULL | 登录密码（MD5加密） |
| code | VARCHAR | 20 | NULL | NULL | 验证码 |
| photo_url | VARCHAR | 255 | NULL | default.png | 头像URL |
| role_id | INT | 11 | NULL | 102 | 角色ID，外键 |
| gender | VARCHAR | 10 | NULL | NULL | 性别 |
| register_date | DATETIME | - | NULL | NULL | 注册日期 |
| status_id | INT | 11 | NULL | 4 | 状态ID |
| creditgrade | INT | 11 | NULL | 5 | 信用等级 |
| count | INT | 11 | NULL | 0 | 交易次数 |
| grade | INT | 11 | NULL | 0 | 积分 |

#### 5.3.2.2 商品表（good_table）

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | 11 | NOT NULL | AUTO_INCREMENT | 商品ID，主键 |
| name | VARCHAR | 255 | NULL | NULL | 商品名称 |
| photo_url | VARCHAR | 200 | NULL | NULL | 商品图片URL |
| first_type_id | INT | 11 | NULL | NULL | 一级分类ID |
| second_type_id | INT | 11 | NULL | NULL | 二级分类ID |
| describe | VARCHAR | 500 | NULL | NULL | 商品描述 |
| upload_date | DATETIME | - | NULL | NULL | 上传日期 |
| price | FLOAT | - | NULL | NULL | 商品价格 |
| status_id | INT | 11 | NULL | NULL | 状态ID |
| user_id | INT | 11 | NULL | NULL | 发布用户ID |
| update | DATETIME | - | NULL | NULL | 更新时间 |

#### 5.3.2.3 订单表（order_table）

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | 11 | NOT NULL | AUTO_INCREMENT | 订单ID，主键 |
| good_name | VARCHAR | 200 | NULL | NULL | 商品名称 |
| seller | VARCHAR | 50 | NULL | NULL | 卖家姓名 |
| seller_id | INT | 11 | NULL | NULL | 卖家ID |
| customer | VARCHAR | 50 | NULL | NULL | 买家姓名 |
| customer_id | INT | 11 | NULL | NULL | 买家ID |
| good_id | INT | 11 | NULL | NULL | 商品ID |
| money | INT | 11 | NULL | NULL | 交易金额 |
| submit_date | DATETIME | - | NULL | NULL | 提交日期 |
| end_date | DATETIME | - | NULL | NULL | 完成日期 |
| status_id | INT | 11 | NULL | NULL | 状态ID |
| order_type | INT | 11 | NULL | NULL | 订单类型 |
| creditend_date | DATETIME | - | NULL | NULL | 信用截止日期 |
| score | INT | 11 | NULL | NULL | 评分 |

#### 5.3.2.4 商品分类表

**一级分类表（first_type_table）**

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | 11 | NOT NULL | - | 分类ID，主键 |
| name | VARCHAR | 50 | NULL | NULL | 分类名称 |

**二级分类表（second_type_table）**

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | 11 | NOT NULL | - | 分类ID，主键 |
| first_type_id | INT | 11 | NULL | NULL | 一级分类ID |
| name | VARCHAR | 50 | NULL | NULL | 分类名称 |

#### 5.3.2.5 其他辅助表

**收藏表（collect_table）**

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | 11 | NOT NULL | AUTO_INCREMENT | 收藏ID，主键 |
| good_id | INT | 11 | NULL | NULL | 商品ID |
| good_name | VARCHAR | 255 | NULL | NULL | 商品名称 |
| user_id | INT | 11 | NULL | NULL | 用户ID |

**评价表（review_table）**

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | 11 | NOT NULL | AUTO_INCREMENT | 评价ID，主键 |
| good_id | INT | 11 | NULL | NULL | 商品ID |
| from_user_id | INT | 11 | NULL | NULL | 评价用户ID |
| from_user | VARCHAR | 255 | NULL | NULL | 评价用户名 |
| to_user_id | INT | 11 | NULL | NULL | 被评价用户ID |
| to_user | VARCHAR | 255 | NULL | NULL | 被评价用户名 |
| text | VARCHAR | 255 | NULL | NULL | 评价内容 |
| upload_date | DATETIME | - | NULL | NULL | 评价日期 |
| status | INT | 11 | NULL | NULL | 状态 |

**角色表（role_table）**

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | 11 | NOT NULL | - | 角色ID，主键 |
| code | VARCHAR | 20 | NOT NULL | - | 角色代码 |
| role | VARCHAR | 20 | NOT NULL | - | 角色名称 |

### 5.3.3 数据库设计说明

1. **主键设计**：所有表都使用自增整数作为主键，保证唯一性和查询效率。

2. **外键约束**：建立适当的外键约束，保证数据的参照完整性。

3. **索引设计**：在经常查询的字段上建立索引，如用户邮箱、商品分类等。

4. **数据类型选择**：根据实际需求选择合适的数据类型，平衡存储空间和查询效率。

5. **字符编码**：统一使用UTF-8编码，支持中文和特殊字符。

6. **数据安全**：敏感信息如密码采用MD5加密存储，保证数据安全。

通过以上总体设计，系统具备了清晰的架构层次、完整的功能模块和合理的数据库结构，为后续的详细设计和系统实现奠定了坚实的基础。
