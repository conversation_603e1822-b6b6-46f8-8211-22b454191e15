# 第四章 需求分析

## 4.1 业务需求分析

### 4.1.1 业务背景

校园二手交易平台是为了满足高校师生对二手物品交易的需求而设计的电子商务平台。该平台主要服务于校园内部用户，提供安全、便捷、高效的二手物品交易服务。

### 4.1.2 业务目标

1. **提高资源利用效率**：通过平台促进闲置物品的流通，减少资源浪费
2. **降低交易成本**：为用户提供低成本的交易渠道，减少中间环节
3. **增强用户体验**：提供简单易用的交易界面和完善的服务流程
4. **保障交易安全**：建立可靠的信用体系和纠纷处理机制

### 4.1.3 业务流程分析

#### 4.1.3.1 用户注册流程

```mermaid
graph TD
    A[访问注册页面] --> B[填写注册信息]
    B --> C[验证信息格式]
    C --> D{信息是否有效?}
    D -->|否| E[显示错误信息]
    E --> B
    D -->|是| F[检查邮箱是否已注册]
    F --> G{邮箱是否已存在?}
    G -->|是| H[提示邮箱已注册]
    H --> B
    G -->|否| I[创建用户账户]
    I --> J[发送验证邮件]
    J --> K[注册成功]
```

#### 4.1.3.2 商品发布流程

```mermaid
graph TD
    A[用户登录] --> B[进入发布页面]
    B --> C[填写商品信息]
    C --> D[上传商品图片]
    D --> E[选择商品分类]
    E --> F[设置价格]
    F --> G[提交审核]
    G --> H{管理员审核}
    H -->|通过| I[商品上架]
    H -->|拒绝| J[返回修改]
    J --> C
    I --> K[发布成功]
```

#### 4.1.3.3 商品交易流程

```mermaid
graph TD
    A[浏览商品] --> B[查看商品详情]
    B --> C[联系卖家]
    C --> D[协商交易细节]
    D --> E[确认购买]
    E --> F[生成订单]
    F --> G[支付定金/全款]
    G --> H[卖家确认]
    H --> I[线下交易]
    I --> J[确认收货]
    J --> K[交易完成]
    K --> L[互相评价]
```

#### 4.1.3.4 后台管理流程

```mermaid
graph TD
    A[管理员登录] --> B[进入管理后台]
    B --> C[选择管理功能]
    C --> D[用户管理]
    C --> E[商品管理]
    C --> F[订单管理]
    C --> G[系统设置]
    D --> H[查看/编辑/删除用户]
    E --> I[审核/上架/下架商品]
    F --> J[处理订单纠纷]
    G --> K[配置系统参数]
```

### 4.1.4 业务规则

1. **用户注册规则**：
   - 必须使用有效的邮箱地址注册
   - 用户名不能重复
   - 密码长度不少于6位

2. **商品发布规则**：
   - 商品必须经过管理员审核才能上架
   - 每个商品必须至少上传一张图片
   - 商品价格必须大于0

3. **交易规则**：
   - 买卖双方必须都是注册用户
   - 交易完成后双方可以互相评价
   - 恶意交易将被记录并影响信用等级

## 4.2 功能需求分析

### 4.2.1 用例图

```mermaid
graph LR
    User[普通用户] --> UC1[注册登录]
    User --> UC2[浏览商品]
    User --> UC3[搜索商品]
    User --> UC4[发布商品]
    User --> UC5[购买商品]
    User --> UC6[管理个人信息]
    User --> UC7[查看订单]
    User --> UC8[收藏商品]
    User --> UC9[评价交易]
    
    Admin[管理员] --> UC10[用户管理]
    Admin --> UC11[商品管理]
    Admin --> UC12[订单管理]
    Admin --> UC13[分类管理]
    Admin --> UC14[系统统计]
```

### 4.2.2 主要用例说明

#### 4.2.2.1 用户注册用例

**用例名称**：用户注册
**参与者**：未注册用户
**前置条件**：用户访问注册页面
**主要流程**：
1. 用户填写注册信息（姓名、邮箱、密码、手机号等）
2. 系统验证信息格式和唯一性
3. 系统创建用户账户
4. 发送注册成功通知

**后置条件**：用户账户创建成功，可以登录系统
**异常流程**：
- 邮箱已被注册：提示用户更换邮箱
- 信息格式错误：提示用户重新填写

#### 4.2.2.2 商品发布用例

**用例名称**：商品发布
**参与者**：注册用户
**前置条件**：用户已登录系统
**主要流程**：
1. 用户进入商品发布页面
2. 填写商品基本信息（名称、描述、价格等）
3. 上传商品图片
4. 选择商品分类
5. 提交发布申请
6. 管理员审核商品信息
7. 审核通过后商品上架

**后置条件**：商品成功发布并可被其他用户浏览
**异常流程**：
- 审核不通过：返回修改建议，用户可重新提交

#### 4.2.2.3 商品购买用例

**用例名称**：商品购买
**参与者**：买家、卖家
**前置条件**：买家已登录，商品处于在售状态
**主要流程**：
1. 买家浏览商品详情
2. 联系卖家协商交易细节
3. 确认购买意向
4. 生成交易订单
5. 线下完成交易
6. 确认收货
7. 双方互相评价

**后置条件**：交易完成，订单状态更新
**异常流程**：
- 商品已售出：提示商品不可购买
- 交易纠纷：可申请管理员介入处理

### 4.2.3 功能模块详细分析

#### 4.2.3.1 用户管理模块

**主要功能**：
1. 用户注册：支持邮箱注册，验证信息有效性
2. 用户登录：支持邮箱+密码登录方式
3. 个人信息管理：修改个人资料、头像上传
4. 密码管理：修改密码、找回密码
5. 信用管理：查看个人信用等级和交易记录

**技术实现**：
- 使用MD5加密存储密码
- Session管理用户登录状态
- 文件上传处理头像图片

#### 4.2.3.2 商品管理模块

**主要功能**：
1. 商品发布：填写商品信息、上传图片、选择分类
2. 商品编辑：修改商品信息、更新图片
3. 商品下架：用户主动下架或管理员强制下架
4. 商品搜索：支持关键词搜索和分类筛选
5. 商品浏览：分页显示、排序功能

**技术实现**：
- 多图片上传和存储
- 全文搜索功能
- 分页查询优化

#### 4.2.3.3 交易管理模块

**主要功能**：
1. 订单生成：创建交易订单记录
2. 订单跟踪：查看订单状态和进度
3. 交易确认：买卖双方确认交易完成
4. 评价系统：交易后互相评价
5. 纠纷处理：处理交易争议

**技术实现**：
- 订单状态机管理
- 评价系统设计
- 消息通知机制

#### 4.2.3.4 后台管理模块

**主要功能**：
1. 用户管理：查看、编辑、禁用用户账户
2. 商品审核：审核待发布商品
3. 订单管理：查看和处理订单信息
4. 分类管理：管理商品分类体系
5. 系统统计：查看平台运营数据

**技术实现**：
- 权限控制和角色管理
- 数据统计和报表生成
- 批量操作功能

## 4.3 非功能需求分析

### 4.3.1 软硬件环境需求

#### 4.3.1.1 服务器端环境

**操作系统要求**：
- Linux（推荐CentOS 7.0以上或Ubuntu 18.04以上）
- Windows Server 2016以上

**软件环境要求**：
- JDK 1.8或以上版本
- Apache Tomcat 8.5或以上版本
- MySQL 5.7或以上版本
- Maven 3.6或以上版本

**硬件配置要求**：
- CPU：4核心2.4GHz以上
- 内存：8GB以上
- 硬盘：100GB以上可用空间
- 网络：100Mbps以上带宽

#### 4.3.1.2 客户端环境

**浏览器支持**：
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 16+
- IE 11+

**移动端支持**：
- iOS 10+
- Android 6.0+

**网络要求**：
- 宽带或移动网络连接
- 建议网速不低于2Mbps

### 4.3.2 性能需求

#### 4.3.2.1 响应时间要求

- 页面加载时间：≤3秒
- 数据查询响应：≤2秒
- 文件上传处理：≤10秒
- 用户操作响应：≤1秒

#### 4.3.2.2 并发性能要求

- 支持同时在线用户数：≥500人
- 支持并发访问数：≥100次/秒
- 数据库连接池：≥50个连接
- 文件上传并发：≥20个

#### 4.3.2.3 可用性要求

- 系统可用性：≥99.5%
- 故障恢复时间：≤30分钟
- 数据备份频率：每日备份
- 系统维护窗口：每周不超过2小时

### 4.3.3 安全性需求

#### 4.3.3.1 数据安全

1. **用户密码安全**：
   - 密码采用MD5加密存储
   - 支持密码强度验证
   - 定期提醒用户更新密码

2. **数据传输安全**：
   - 采用HTTPS协议传输敏感数据
   - 重要操作需要二次验证
   - 防止SQL注入和XSS攻击

3. **数据存储安全**：
   - 定期备份重要数据
   - 敏感信息加密存储
   - 访问日志记录和监控

#### 4.3.3.2 系统安全

1. **访问控制**：
   - 基于角色的权限管理
   - 登录失败次数限制
   - 异常登录检测和通知

2. **系统防护**：
   - 防火墙配置
   - 入侵检测系统
   - 定期安全漏洞扫描

### 4.3.4 并发性需求

#### 4.3.4.1 用户并发

- 支持多用户同时注册、登录
- 支持多用户同时浏览商品
- 支持多用户同时发布商品
- 支持多用户同时进行交易

#### 4.3.4.2 数据并发

- 防止商品重复购买
- 保证库存数据一致性
- 处理订单并发创建
- 避免数据竞争条件

#### 4.3.4.3 并发控制策略

- 使用数据库事务保证数据一致性
- 采用乐观锁处理并发更新
- 使用连接池管理数据库连接
- 实施缓存策略提高性能

### 4.3.5 前端界面需求

#### 4.3.5.1 用户界面设计原则

1. **简洁性**：界面布局清晰，操作简单直观
2. **一致性**：保持整体设计风格统一
3. **易用性**：符合用户使用习惯，学习成本低
4. **美观性**：色彩搭配合理，视觉效果良好

#### 4.3.5.2 响应式设计要求

- 支持PC端、平板、手机等多种设备
- 自适应不同屏幕尺寸
- 保证在各种设备上的良好体验
- 优化移动端操作体验

#### 4.3.5.3 交互设计要求

- 提供清晰的操作反馈
- 重要操作需要确认提示
- 错误信息友好易懂
- 支持键盘快捷操作

通过以上详细的需求分析，为系统设计和开发提供了明确的指导方向，确保最终产品能够满足用户需求和业务目标。
