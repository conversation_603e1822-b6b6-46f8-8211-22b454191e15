# 第七章 项目实现

## 7.1 开发环境搭建

### 7.1.1 开发工具配置

**集成开发环境**：
- IDE：IntelliJ IDEA 2020.3
- JDK：Oracle JDK 1.8.0_281
- Maven：Apache Maven 3.6.3
- 数据库工具：Navicat Premium 15

**服务器环境**：
- Web服务器：Apache Tomcat 8.5.65
- 数据库：MySQL 8.0.28
- 操作系统：Windows 10 / CentOS 7

### 7.1.2 项目结构

```
wzh-secondshop/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/wzh/secondshop/
│   │   │       ├── controllers/     # 控制器层
│   │   │       ├── services/        # 服务层
│   │   │       ├── mappers/         # 数据访问层
│   │   │       ├── models/          # 实体类
│   │   │       ├── utils/           # 工具类
│   │   │       └── exception/       # 异常处理
│   │   ├── resources/
│   │   │   ├── spring/              # Spring配置文件
│   │   │   └── logback.xml          # 日志配置
│   │   └── webapp/
│   │       ├── WEB-INF/
│   │       │   ├── views/           # JSP页面
│   │       │   └── web.xml          # Web配置
│   │       └── statics/             # 静态资源
│   │           ├── css/
│   │           ├── js/
│   │           ├── images/
│   │           └── bootstrap-3.3.0/
└── pom.xml                          # Maven配置文件
```

## 7.2 核心功能实现

### 7.2.1 用户管理功能实现

#### ******* 用户实体类

<augment_code_snippet path="secondHandShop_vue_ssm-master/wzh-secondshop/src/main/java/com/wzh/secondshop/models/User.java" mode="EXCERPT">
````java
public class User {
    private int id;
    private String name;
    private String mobile;
    private String email;
    private String password;
    private String code;
    private String photoUrl;
    private int roleId;
    private String gender;
    private Date registerDate;
    private int statusId;
    private int creditgrade;
    private int count;
    private int grade;
    
    // getter和setter方法
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    // ... 其他getter/setter方法
}
````
</augment_code_snippet>

#### ******* 用户数据访问层

<augment_code_snippet path="secondHandShop_vue_ssm-master/wzh-secondshop/src/main/java/com/wzh/secondshop/mappers/UserMapper.java" mode="EXCERPT">
````java
public interface UserMapper {
    @Select("select * from user_table;")
    List<User> getAllUser();

    @Select("select * from user_table where id = #{id}")
    User getUserById(int id);

    @Select("select * from user_table where email = #{email}")
    User getUserByEmail(String email);

    @Insert("insert into user_table (name, mobile, email, password, code, photo_url, role_id, gender, register_date, status_id, creditgrade, count, grade) " +
            "values (#{name}, #{mobile}, #{email}, #{password}, #{code}, '/statics/image/photos/default/default.png', 102, #{gender}, now(), 4, 5, 0, 0);")
    int insertUser(User user);
}
````
</augment_code_snippet>

#### ******* 用户服务层实现

```java
@Service
@Transactional
public class UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    /**
     * 用户注册
     */
    public boolean registerUser(User user) {
        try {
            // 检查邮箱是否已存在
            User existUser = userMapper.getUserByEmail(user.getEmail());
            if (existUser != null) {
                return false;
            }
            
            // 生成验证码
            String code = RandomString.getRandomString(5);
            user.setCode(code);
            
            // 密码加密
            String encryptedPassword = DigestUtils.md5DigestAsHex(
                (user.getPassword() + code).getBytes());
            user.setPassword(encryptedPassword);
            
            // 保存用户
            int result = userMapper.insertUser(user);
            return result > 0;
        } catch (Exception e) {
            logger.error("用户注册失败", e);
            return false;
        }
    }
    
    /**
     * 用户登录验证
     */
    public User validateUser(String email, String password) {
        User user = userMapper.getUserByEmail(email);
        if (user != null) {
            String encryptedPassword = DigestUtils.md5DigestAsHex(
                (password + user.getCode()).getBytes());
            if (encryptedPassword.equals(user.getPassword())) {
                return user;
            }
        }
        return null;
    }
}
```

#### 7.2.1.4 用户控制器实现

```java
@Controller
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 用户注册处理
     */
    @RequestMapping(value = "/register", method = RequestMethod.POST)
    public String registerSubmit(@ModelAttribute User user, ModelMap model) {
        // 验证输入参数
        if (!InfoCheck.checkEmail(user.getEmail())) {
            model.addAttribute("message", "邮箱格式不正确");
            return "home/register";
        }
        
        if (!InfoCheck.checkMobile(user.getMobile())) {
            model.addAttribute("message", "手机号格式不正确");
            return "home/register";
        }
        
        // 执行注册
        boolean success = userService.registerUser(user);
        if (success) {
            model.addAttribute("message", "注册成功，请登录");
            return "home/login";
        } else {
            model.addAttribute("message", "注册失败，邮箱可能已被注册");
            return "home/register";
        }
    }
    
    /**
     * 用户登录处理
     */
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public String loginSubmit(@RequestParam String email,
                             @RequestParam String password,
                             HttpSession session,
                             ModelMap model) {
        User user = userService.validateUser(email, password);
        if (user != null && user.getStatusId() == 4) {
            session.setAttribute("user", user);
            return "redirect:/";
        } else {
            model.addAttribute("message", "登录失败，请检查邮箱和密码");
            return "home/login";
        }
    }
}
```

### 7.2.2 商品管理功能实现

#### 7.2.2.1 商品实体类

```java
public class Good {
    private int id;
    private String name;
    private String photoUrl;
    private int firstTypeId;
    private int secondTypeId;
    private String describe;
    private Date uploadDate;
    private float price;
    private int statusId;
    private int userId;
    private Date update;
    
    // 关联对象
    private User user;
    private FirstType firstType;
    private SecondType secondType;
    private List<Image> images;
    
    // getter和setter方法
    // ...
}
```

#### 7.2.2.2 商品数据访问层

```java
public interface GoodMapper {
    
    @Select("select * from good_table where status_id = 0 order by upload_date desc limit #{offset}, #{limit}")
    List<Good> getGoodsByPage(@Param("offset") int offset, @Param("limit") int limit);
    
    @Select("select * from good_table where id = #{id}")
    Good getGoodById(int id);
    
    @Select("select * from good_table where user_id = #{userId} order by upload_date desc")
    List<Good> getGoodsByUserId(int userId);
    
    @Insert("insert into good_table (name, photo_url, first_type_id, second_type_id, describe, upload_date, price, status_id, user_id, update) " +
            "values (#{name}, #{photoUrl}, #{firstTypeId}, #{secondTypeId}, #{describe}, now(), #{price}, 0, #{userId}, now())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertGood(Good good);
    
    @Update("update good_table set status_id = #{statusId}, update = now() where id = #{id}")
    int updateGoodStatus(@Param("id") int id, @Param("statusId") int statusId);
    
    // 搜索商品
    @Select("<script>" +
            "select * from good_table where status_id = 0 " +
            "<if test='searchText != null and searchText != \"\"'>" +
            "and (name like concat('%', #{searchText}, '%') or describe like concat('%', #{searchText}, '%')) " +
            "</if>" +
            "<if test='secondTypeId != null'>" +
            "and second_type_id = #{secondTypeId} " +
            "</if>" +
            "order by upload_date desc limit #{offset}, #{limit}" +
            "</script>")
    List<Good> getGoodsBySearchAndType(@Param("searchText") String searchText,
                                      @Param("secondTypeId") Integer secondTypeId,
                                      @Param("offset") int offset,
                                      @Param("limit") int limit);
}
```

#### ******* 商品服务层实现

```java
@Service
@Transactional
public class GoodService {
    
    @Autowired
    private GoodMapper goodMapper;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private TypeService typeService;
    
    /**
     * 发布商品
     */
    public boolean publishGood(Good good, MultipartFile file, HttpServletRequest request) {
        try {
            // 处理图片上传
            if (file != null && !file.isEmpty()) {
                String photoUrl = handleFileUpload(file, request);
                good.setPhotoUrl(photoUrl);
            } else {
                good.setPhotoUrl("/statics/image/goods/default/nophoto.png");
            }
            
            // 保存商品信息
            int result = goodMapper.insertGood(good);
            return result > 0;
        } catch (Exception e) {
            logger.error("商品发布失败", e);
            return false;
        }
    }
    
    /**
     * 处理文件上传
     */
    private String handleFileUpload(MultipartFile file, HttpServletRequest request) {
        try {
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String newFilename = System.currentTimeMillis() + RandomString.getRandomString(8) + extension;
            
            String uploadPath = request.getServletContext().getRealPath("/statics/image/goods/");
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }
            
            File targetFile = new File(uploadDir, newFilename);
            file.transferTo(targetFile);
            
            return "/statics/image/goods/" + newFilename;
        } catch (Exception e) {
            logger.error("文件上传失败", e);
            return "/statics/image/goods/default/nophoto.png";
        }
    }
    
    /**
     * 获取商品详情
     */
    public Good getGoodDetail(int goodId) {
        Good good = goodMapper.getGoodById(goodId);
        if (good != null) {
            // 加载关联信息
            good.setUser(userService.getUserById(good.getUserId()));
            good.setFirstType(typeService.getFirstTypeById(good.getFirstTypeId()));
            good.setSecondType(typeService.getSecondTypeById(good.getSecondTypeId()));
        }
        return good;
    }
}
```

### 7.2.3 交易管理功能实现

#### 7.2.3.1 订单实体类

```java
public class Order {
    private int id;
    private String goodName;
    private String seller;
    private int sellerId;
    private String customer;
    private int customerId;
    private int goodId;
    private int money;
    private Date submitDate;
    private Date endDate;
    private int statusId;
    private int orderType;
    private Date creditendDate;
    private int score;
    
    // 关联对象
    private Good good;
    private User sellerUser;
    private User customerUser;
    
    // getter和setter方法
    // ...
}
```

#### 7.2.3.2 订单数据访问层

```java
public interface OrderMapper {
    
    @Select("select * from order_table order by submit_date desc")
    List<Order> getOrderList();
    
    @Select("select * from order_table where id = #{id}")
    Order getOrderById(int id);
    
    @Select("select * from order_table where customer_id = #{customerId} order by submit_date desc")
    List<Order> getOrdersByCustomerId(int customerId);
    
    @Select("select * from order_table where seller_id = #{sellerId} order by submit_date desc")
    List<Order> getOrdersBySellerId(int sellerId);
    
    @Insert("insert into order_table (good_name, seller, seller_id, customer, customer_id, good_id, money, submit_date, status_id, order_type) " +
            "values (#{goodName}, #{seller}, #{sellerId}, #{customer}, #{customerId}, #{goodId}, #{money}, now(), 2, 0)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertOrder(Order order);
    
    @Update("update order_table set status_id = #{statusId}, end_date = now() where id = #{id}")
    int updateOrderStatus(@Param("id") int id, @Param("statusId") int statusId);
}
```

#### 7.2.3.3 订单服务层实现

```java
@Service
@Transactional
public class OrderService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private GoodService goodService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 创建订单
     */
    public boolean createOrder(int goodId, int customerId) {
        try {
            Good good = goodService.getGoodById(goodId);
            User customer = userService.getUserById(customerId);
            User seller = userService.getUserById(good.getUserId());
            
            // 检查商品状态
            if (good.getStatusId() != 0) {
                return false; // 商品不可购买
            }
            
            // 创建订单
            Order order = new Order();
            order.setGoodName(good.getName());
            order.setSeller(seller.getName());
            order.setSellerId(seller.getId());
            order.setCustomer(customer.getName());
            order.setCustomerId(customer.getId());
            order.setGoodId(goodId);
            order.setMoney((int)good.getPrice());
            
            int result = orderMapper.insertOrder(order);
            
            if (result > 0) {
                // 更新商品状态为已售
                goodService.updateGoodStatus(goodId, 1);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            logger.error("创建订单失败", e);
            return false;
        }
    }
    
    /**
     * 完成交易
     */
    public boolean completeOrder(int orderId) {
        try {
            int result = orderMapper.updateOrderStatus(orderId, 3);
            return result > 0;
        } catch (Exception e) {
            logger.error("完成订单失败", e);
            return false;
        }
    }
}
```

## 7.3 前端页面实现

### 7.3.1 主页面实现

<augment_code_snippet path="secondHandShop_vue_ssm-master/wzh-secondshop/src/main/webapp/WEB-INF/views/home/<USER>" mode="EXCERPT">
````jsp
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>校园二手交易平台</title>
    <link rel="stylesheet" href="<c:url value="/statics/bootstrap-3.3.0/css/bootstrap.css"/>">
    <link rel="stylesheet" href="<c:url value="/statics/css/style.css"/>">
</head>
<body>
    <div>
        <jsp:include page="header.jsp" />
    </div>
    
    <div class="container">
        <div class="main-content">
            <!-- 商品分类导航 -->
            <div class="col-md-2" id="type-bar">
                <c:forEach var="firstType" items="${firstTypes}">
                    <div class="btn-type" onclick="typeButton(${firstType.id})">
                        <h4>${firstType.name}</h4>
                    </div>
                </c:forEach>
            </div>
            
            <!-- 商品列表 -->
            <div class="col-md-10">
                <div class="row">
                    <c:forEach var="good" items="${goods}">
                        <div class="col-md-3">
                            <div class="good-item">
                                <a href="<c:url value="/goods/goodInfo?goodId=${good.id}"/>">
                                    <img src="<c:url value="${good.photoUrl}"/>" class="img-responsive">
                                    <h5>${good.name}</h5>
                                    <p class="price">￥${good.price}</p>
                                </a>
                            </div>
                        </div>
                    </c:forEach>
                </div>
            </div>
        </div>
    </div>
    
    <script src="<c:url value="/statics/jquery-1.12.4/jquery-1.12.4.js"/>"></script>
    <script src="<c:url value="/statics/bootstrap-3.3.0/js/bootstrap.js"/>"></script>
</body>
</html>
````
</augment_code_snippet>

### 7.3.2 用户登录页面

```jsp
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>用户登录</title>
    <link rel="stylesheet" href="<c:url value="/statics/bootstrap-3.3.0/css/bootstrap.css"/>">
    <link rel="stylesheet" href="<c:url value="/statics/css/style.css"/>">
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-4 col-md-offset-4">
                <div class="login-panel panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">用户登录</h3>
                    </div>
                    <div class="panel-body">
                        <form role="form" action="<c:url value="/login"/>" method="post">
                            <fieldset>
                                <div class="form-group">
                                    <input class="form-control" placeholder="邮箱" name="email" type="email" required>
                                </div>
                                <div class="form-group">
                                    <input class="form-control" placeholder="密码" name="password" type="password" required>
                                </div>
                                <button type="submit" class="btn btn-primary btn-block">登录</button>
                                <a href="<c:url value="/register"/>" class="btn btn-link btn-block">注册新账户</a>
                            </fieldset>
                        </form>
                        <c:if test="${not empty message}">
                            <div class="alert alert-danger">${message}</div>
                        </c:if>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
```

### 7.3.3 商品发布页面

```jsp
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>发布商品</title>
    <link rel="stylesheet" href="<c:url value="/statics/bootstrap-3.3.0/css/bootstrap.css"/>">
</head>
<body>
    <div class="container">
        <h2>发布商品</h2>
        <form action="<c:url value="/goods/publishGood"/>" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label>商品名称</label>
                <input type="text" class="form-control" name="name" required>
            </div>
            <div class="form-group">
                <label>商品描述</label>
                <textarea class="form-control" name="describe" rows="4"></textarea>
            </div>
            <div class="form-group">
                <label>商品价格</label>
                <input type="number" class="form-control" name="price" step="0.01" required>
            </div>
            <div class="form-group">
                <label>商品分类</label>
                <select class="form-control" name="firstTypeId" required>
                    <option value="">请选择分类</option>
                    <c:forEach var="type" items="${firstTypes}">
                        <option value="${type.id}">${type.name}</option>
                    </c:forEach>
                </select>
            </div>
            <div class="form-group">
                <label>商品图片</label>
                <input type="file" class="form-control" name="file" accept="image/*">
            </div>
            <button type="submit" class="btn btn-primary">发布商品</button>
            <a href="<c:url value="/"/>" class="btn btn-default">取消</a>
        </form>
    </div>
</body>
</html>
```

## 7.4 系统配置实现

### 7.4.1 Spring配置

<augment_code_snippet path="secondHandShop_vue_ssm-master/wzh-secondshop/src/main/resources/spring/mybatis.xml" mode="EXCERPT">
````xml
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx">
    
    <!-- 数据源配置 -->
    <bean id="dataSource" class="org.apache.commons.dbcp.BasicDataSource">
        <property name="driverClassName" value="com.mysql.cj.jdbc.Driver"/>
        <property name="url" value="**************************************************************************************************"/>
        <property name="username" value="root"/>
        <property name="password" value="123456"/>
        <property name="maxActive" value="10"/>
        <property name="maxIdle" value="2"/>
        <property name="maxWait" value="12000"/>
    </bean>

    <!-- 事务管理器 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <!-- MyBatis配置 -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="configuration">
            <bean class="org.apache.ibatis.session.Configuration">
                <property name="mapUnderscoreToCamelCase" value="true"/>
                <property name="cacheEnabled" value="false"/>
                <property name="lazyLoadingEnabled" value="false"/>
                <property name="useGeneratedKeys" value="true"/>
            </bean>
        </property>
    </bean>
</beans>
````
</augment_code_snippet>

### 7.4.2 Maven依赖配置

<augment_code_snippet path="secondHandShop_vue_ssm-master/wzh-secondshop/pom.xml" mode="EXCERPT">
````xml
<dependencies>
    <!-- Spring框架 -->
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context</artifactId>
        <version>4.3.3.RELEASE</version>
    </dependency>
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>
        <version>4.3.3.RELEASE</version>
    </dependency>
    
    <!-- MyBatis -->
    <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>3.4.0</version>
    </dependency>
    <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis-spring</artifactId>
        <version>1.3.0</version>
    </dependency>
    
    <!-- MySQL驱动 -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>8.0.28</version>
    </dependency>
    
    <!-- 文件上传 -->
    <dependency>
        <groupId>commons-fileupload</groupId>
        <artifactId>commons-fileupload</artifactId>
        <version>1.3.1</version>
    </dependency>
</dependencies>
````
</augment_code_snippet>

通过以上实现，系统的核心功能已经完成，包括用户管理、商品管理、交易管理等主要模块，前端页面采用JSP+Bootstrap实现，后端采用SSM框架，数据库使用MySQL，整个系统架构清晰，功能完整。
