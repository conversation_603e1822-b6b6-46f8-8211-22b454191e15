# 第六章 项目详细设计

## 6.1 用户管理模块详细设计

### 6.1.1 用户注册模块

#### 6.1.1.1 流程图

```mermaid
flowchart TD
    A[开始] --> B[用户访问注册页面]
    B --> C[填写注册信息]
    C --> D[前端验证信息格式]
    D --> E{格式是否正确?}
    E -->|否| F[显示错误提示]
    F --> C
    E -->|是| G[提交注册请求]
    G --> H[后端验证信息]
    H --> I{邮箱是否已存在?}
    I -->|是| J[返回邮箱已注册错误]
    J --> F
    I -->|否| K[生成验证码]
    K --> L[密码MD5加密]
    L --> M[保存用户信息到数据库]
    M --> N{保存是否成功?}
    N -->|否| O[返回系统错误]
    O --> F
    N -->|是| P[注册成功]
    P --> Q[跳转到登录页面]
    Q --> R[结束]
```

#### 6.1.1.2 类图

```mermaid
classDiagram
    class User {
        -int id
        -String name
        -String mobile
        -String email
        -String password
        -String code
        -String photoUrl
        -int roleId
        -String gender
        -Date registerDate
        -int statusId
        -int creditgrade
        -int count
        -int grade
        +getId() int
        +setName(String) void
        +getEmail() String
        +setPassword(String) void
    }
    
    class UserController {
        -UserService userService
        +register(User) String
        +login(String, String) String
        +logout() String
        +updateProfile(User) String
    }
    
    class UserService {
        -UserMapper userMapper
        +registerUser(User) boolean
        +getUserByEmail(String) User
        +updateUser(User) boolean
        +validateUser(String, String) User
    }
    
    class UserMapper {
        +insertUser(User) int
        +getUserByEmail(String) User
        +getUserById(int) User
        +updateUser(User) int
    }
    
    UserController --> UserService
    UserService --> UserMapper
    UserService --> User
    UserMapper --> User
```

#### ******* 时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as Controller
    participant S as Service
    participant M as Mapper
    participant DB as 数据库
    
    U->>C: 提交注册信息
    C->>C: 验证参数格式
    C->>S: 调用注册服务
    S->>M: 检查邮箱是否存在
    M->>DB: 查询用户表
    DB-->>M: 返回查询结果
    M-->>S: 返回用户信息
    alt 邮箱已存在
        S-->>C: 返回错误信息
        C-->>U: 显示邮箱已注册
    else 邮箱不存在
        S->>S: 生成验证码
        S->>S: 密码MD5加密
        S->>M: 保存用户信息
        M->>DB: 插入用户记录
        DB-->>M: 返回插入结果
        M-->>S: 返回操作结果
        S-->>C: 返回注册成功
        C-->>U: 跳转登录页面
    end
```

### 6.1.2 用户登录模块

#### ******* 流程图

```mermaid
flowchart TD
    A[开始] --> B[用户访问登录页面]
    B --> C[输入邮箱和密码]
    C --> D[前端验证输入格式]
    D --> E{格式是否正确?}
    E -->|否| F[显示格式错误提示]
    F --> C
    E -->|是| G[提交登录请求]
    G --> H[根据邮箱查询用户]
    H --> I{用户是否存在?}
    I -->|否| J[返回用户不存在错误]
    J --> F
    I -->|是| K[验证密码]
    K --> L{密码是否正确?}
    L -->|否| M[返回密码错误]
    M --> F
    L -->|是| N{用户状态是否正常?}
    N -->|否| O[返回账户异常错误]
    O --> F
    N -->|是| P[创建Session]
    P --> Q[登录成功]
    Q --> R[跳转到首页]
    R --> S[结束]
```

#### ******* 关键代码实现

<augment_code_snippet path="secondHandShop_vue_ssm-master/wzh-secondshop/src/main/java/com/wzh/secondshop/controllers/HomeController.java" mode="EXCERPT">
````java
@RequestMapping(value = "/login", method = RequestMethod.POST)
public String loginSubmit(ModelMap model,
                          @RequestParam(value = "email", required = false) String email,
                          @RequestParam(value = "password", required = false) String password,
                          HttpSession session) {
    User user = userService.getUserByEmail(email);
    String message;
    if (user != null){
        String mdsPass = DigestUtils.md5DigestAsHex((password + user.getCode()).getBytes());
        if (!mdsPass .equals(user.getPassword())){
            message = "用户密码错误！";
        } else {
            if (user.getStatusId() == 4){
                session.setAttribute("user",user);
                return "redirect:/";
            } else {
                message = "用户已失效！";
            }
        }
    }
}
````
</augment_code_snippet>

## 6.2 商品管理模块详细设计

### 6.2.1 商品发布模块

#### ******* 流程图

```mermaid
flowchart TD
    A[开始] --> B[用户登录验证]
    B --> C{是否已登录?}
    C -->|否| D[跳转登录页面]
    D --> E[结束]
    C -->|是| F[进入商品发布页面]
    F --> G[填写商品信息]
    G --> H[上传商品图片]
    H --> I[选择商品分类]
    I --> J[设置商品价格]
    J --> K[提交发布请求]
    K --> L[后端验证商品信息]
    L --> M{信息是否完整?}
    M -->|否| N[返回验证错误]
    N --> O[显示错误信息]
    O --> G
    M -->|是| P[保存商品信息]
    P --> Q[设置商品状态为待审核]
    Q --> R[发布成功]
    R --> S[跳转到我的商品页面]
    S --> T[结束]
```

#### ******* 类图

```mermaid
classDiagram
    class Good {
        -int id
        -String name
        -String photoUrl
        -int firstTypeId
        -int secondTypeId
        -String describe
        -Date uploadDate
        -float price
        -int statusId
        -int userId
        -Date update
        +getId() int
        +setName(String) void
        +getPrice() float
        +setUserId(int) void
    }
    
    class GoodController {
        -GoodService goodService
        -TypeService typeService
        +publishGood() String
        +saveGood(Good) String
        +getGoodInfo(int) String
        +searchGoods(String) String
    }
    
    class GoodService {
        -GoodMapper goodMapper
        +saveGood(Good) boolean
        +getGoodById(int) Good
        +getGoodsByUser(int) List~Good~
        +searchGoods(String) List~Good~
    }
    
    class GoodMapper {
        +insertGood(Good) int
        +updateGood(Good) int
        +getGoodById(int) Good
        +getGoodsByUserId(int) List~Good~
    }
    
    GoodController --> GoodService
    GoodService --> GoodMapper
    GoodService --> Good
    GoodMapper --> Good
```

#### ******* 时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as Controller
    participant S as Service
    participant M as Mapper
    participant DB as 数据库
    participant FS as 文件系统
    
    U->>C: 提交商品信息
    C->>C: 验证用户登录状态
    C->>FS: 保存上传的图片
    FS-->>C: 返回图片URL
    C->>S: 调用保存商品服务
    S->>S: 验证商品信息
    S->>M: 保存商品到数据库
    M->>DB: 插入商品记录
    DB-->>M: 返回插入结果
    M-->>S: 返回操作结果
    S-->>C: 返回保存结果
    C-->>U: 显示发布成功页面
```

### 6.2.2 商品搜索模块

#### ******* 流程图

```mermaid
flowchart TD
    A[开始] --> B[用户输入搜索关键词]
    B --> C[选择商品分类(可选)]
    C --> D[点击搜索按钮]
    D --> E[构建搜索条件]
    E --> F[执行数据库查询]
    F --> G[获取商品列表]
    G --> H{是否有搜索结果?}
    H -->|否| I[显示无结果提示]
    H -->|是| J[分页处理结果]
    J --> K[渲染商品列表页面]
    K --> L[显示搜索结果]
    L --> M[结束]
    I --> M
```

#### ******* 关键代码实现

<augment_code_snippet path="secondHandShop_vue_ssm-master/wzh-secondshop/src/main/java/com/wzh/secondshop/controllers/GoodController.java" mode="EXCERPT">
````java
@RequestMapping(value = "/", method = RequestMethod.GET)
public String getHomeGoods(
        ModelMap model,
        @RequestParam(required = false, defaultValue = "") String searchText,
        @RequestParam(required = false) Integer secondTypeId,
        @RequestParam(required = false, defaultValue = "0") int offset,
        @RequestParam(required = false, defaultValue = "40") int limit) {
    List<Good> goods = goodService.getGoodsBySearchAndType(searchText,
            secondTypeId, offset, limit);
    double goodsNum = goodService.getGoodsBySearchAndTypeCount(searchText,
            secondTypeId);
    List<FirstType> firstTypes = typeService.getAllFirstType();
    model.addAttribute("goods", goods);
    model.addAttribute("pages", Math.ceil(goodsNum / limit));
    return "home/homeGoods";
}
````
</augment_code_snippet>

## 6.3 交易管理模块详细设计

### 6.3.1 订单创建模块

#### ******* 流程图

```mermaid
flowchart TD
    A[开始] --> B[买家浏览商品详情]
    B --> C[点击购买按钮]
    C --> D{用户是否登录?}
    D -->|否| E[跳转登录页面]
    E --> F[登录后返回商品页面]
    F --> C
    D -->|是| G{商品是否可购买?}
    G -->|否| H[显示商品不可购买]
    H --> I[结束]
    G -->|是| J[确认购买信息]
    J --> K[创建订单记录]
    K --> L[更新商品状态]
    L --> M[发送通知给卖家]
    M --> N[订单创建成功]
    N --> O[跳转到订单详情页]
    O --> P[结束]
```

#### ******* 类图

```mermaid
classDiagram
    class Order {
        -int id
        -String goodName
        -String seller
        -int sellerId
        -String customer
        -int customerId
        -int goodId
        -int money
        -Date submitDate
        -Date endDate
        -int statusId
        -int orderType
        -Date creditendDate
        -int score
        +getId() int
        +setGoodId(int) void
        +getMoney() int
        +setStatusId(int) void
    }
    
    class OrderController {
        -OrderService orderService
        -GoodService goodService
        +createOrder(int, int) String
        +getOrderList() String
        +updateOrderStatus(int, int) String
    }
    
    class OrderService {
        -OrderMapper orderMapper
        +createOrder(Order) boolean
        +getOrderById(int) Order
        +getOrdersByUser(int) List~Order~
        +updateOrderStatus(int, int) boolean
    }
    
    class OrderMapper {
        +insertOrder(Order) int
        +updateOrder(Order) int
        +getOrderById(int) Order
        +getOrdersByUserId(int) List~Order~
    }
    
    OrderController --> OrderService
    OrderService --> OrderMapper
    OrderService --> Order
    OrderMapper --> Order
```

#### ******* 时序图

```mermaid
sequenceDiagram
    participant B as 买家
    participant C as Controller
    participant OS as OrderService
    participant GS as GoodService
    participant OM as OrderMapper
    participant GM as GoodMapper
    participant DB as 数据库
    
    B->>C: 点击购买商品
    C->>GS: 检查商品状态
    GS->>GM: 查询商品信息
    GM->>DB: 查询商品表
    DB-->>GM: 返回商品信息
    GM-->>GS: 返回商品对象
    GS-->>C: 返回商品状态
    alt 商品可购买
        C->>OS: 创建订单
        OS->>OM: 保存订单信息
        OM->>DB: 插入订单记录
        DB-->>OM: 返回插入结果
        OM-->>OS: 返回操作结果
        OS->>GS: 更新商品状态
        GS->>GM: 更新商品为已售
        GM->>DB: 更新商品表
        DB-->>GM: 返回更新结果
        GM-->>GS: 返回操作结果
        GS-->>OS: 返回更新结果
        OS-->>C: 返回订单创建结果
        C-->>B: 显示订单成功页面
    else 商品不可购买
        C-->>B: 显示商品不可购买提示
    end
```

## 6.4 后台管理模块详细设计

### 6.4.1 管理员登录模块

#### ******* 流程图

```mermaid
flowchart TD
    A[开始] --> B[管理员访问登录页面]
    B --> C[输入邮箱和密码]
    C --> D[提交登录请求]
    D --> E[验证管理员身份]
    E --> F{用户是否存在?}
    F -->|否| G[返回用户不存在错误]
    G --> H[显示错误信息]
    H --> C
    F -->|是| I{密码是否正确?}
    I -->|否| J[返回密码错误]
    J --> H
    I -->|是| K{是否有管理员权限?}
    K -->|否| L[返回权限不足错误]
    L --> H
    K -->|是| M[创建管理员Session]
    M --> N[登录成功]
    N --> O[跳转到管理后台]
    O --> P[结束]
```

#### ******* 关键代码实现

<augment_code_snippet path="secondHandShop_vue_ssm-master/wzh-secondshop/src/main/java/com/wzh/secondshop/controllers/AdminController.java" mode="EXCERPT">
````java
@RequestMapping(value = "/adminLogin", method = RequestMethod.POST)
public String postAdminLogin(ModelMap model,
                             @RequestParam(value = "email", required = false) String email,
                             @RequestParam(value = "password", required = false) String password,
                             HttpSession session) {
    User admin = userService.getUserByEmail(email);
    String message;
    if (admin != null){
        if (!password .equals(admin.getPassword())){
            message = "用户密码错误！";
        } else if (admin.getRoleId() != 101){
            message = "用户没有权限访问！";
        } else {
            session.setAttribute("admin",admin);
            return "redirect:/admin/adminPage";
        }
    }
}
````
</augment_code_snippet>

### 6.4.2 商品审核模块

#### ******* 流程图

```mermaid
flowchart TD
    A[开始] --> B[管理员登录后台]
    B --> C[进入商品管理页面]
    C --> D[查看待审核商品列表]
    D --> E[选择商品进行审核]
    E --> F[查看商品详细信息]
    F --> G{商品信息是否合规?}
    G -->|否| H[拒绝审核]
    H --> I[填写拒绝原因]
    I --> J[更新商品状态为拒绝]
    J --> K[发送通知给发布者]
    G -->|是| L[通过审核]
    L --> M[更新商品状态为上架]
    M --> N[商品正式发布]
    N --> O[发送通知给发布者]
    O --> P[结束]
    K --> P
```

## 6.5 系统安全设计

### 6.5.1 用户认证与授权

1. **密码加密**：使用MD5算法对用户密码进行加密存储
2. **Session管理**：使用HttpSession管理用户登录状态
3. **权限控制**：基于角色的访问控制，区分普通用户和管理员
4. **登录验证**：关键操作前验证用户登录状态

### 6.5.2 数据验证

1. **前端验证**：JavaScript验证用户输入格式
2. **后端验证**：服务器端验证数据完整性和合法性
3. **SQL注入防护**：使用MyBatis参数化查询防止SQL注入
4. **XSS防护**：对用户输入进行HTML转义处理

### 6.5.3 文件上传安全

1. **文件类型限制**：只允许上传指定类型的图片文件
2. **文件大小限制**：限制上传文件的最大尺寸
3. **文件名处理**：重命名上传文件，防止路径遍历攻击
4. **存储路径控制**：将上传文件存储在安全目录中

通过以上详细设计，系统的各个模块都有了明确的实现方案，包括流程图、类图、时序图等多个维度的设计文档，为系统开发提供了详细的技术指导。
